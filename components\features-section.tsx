import { Shield, Clock, Star, Headphones } from 'lucide-react'

export function FeaturesSection() {
  const features = [
    {
      icon: Shield,
      title: 'Fully Insured',
      description: 'All our vehicles come with comprehensive insurance coverage for your complete peace of mind.',
    },
    {
      icon: Clock,
      title: '24/7 Availability',
      description: 'Book and pick up your car anytime with our round-the-clock service availability.',
    },
    {
      icon: Star,
      title: 'Premium Quality',
      description: 'Every vehicle in our fleet is maintained to the highest standards of quality and cleanliness.',
    },
    {
      icon: Headphones,
      title: 'Expert Support',
      description: 'Our dedicated support team is always ready to assist you with any questions or concerns.',
    },
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Why Choose Bendriouch Cars?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            We're committed to providing you with the best car rental experience through our exceptional service and premium vehicles.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center group">
              <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/20 transition-colors">
                <feature.icon className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}