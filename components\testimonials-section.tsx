import { Star } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'

export function TestimonialsSection() {
  const testimonials = [
    {
      name: '<PERSON>',
      rating: 5,
      text: 'Amazing service! The car was in perfect condition and the booking process was seamless. Highly recommended!',
      location: 'New York, NY',
    },
    {
      name: '<PERSON>',
      rating: 5,
      text: 'Professional service from start to finish. The team went above and beyond to ensure I had everything I needed.',
      location: 'Los Angeles, CA',
    },
    {
      name: '<PERSON>',
      rating: 5,
      text: 'Great selection of vehicles and competitive prices. The customer support was exceptional throughout my rental period.',
      location: 'Miami, FL',
    },
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            What Our Customers Say
          </h2>
          <p className="text-xl text-gray-600">
            Don't just take our word for it - hear from our satisfied customers
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  "{testimonial.text}"
                </p>
                <div>
                  <div className="font-semibold text-gray-900">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {testimonial.location}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}