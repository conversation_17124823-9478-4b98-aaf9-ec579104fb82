'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Calendar, MapPin, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'

export function HeroSection() {
  const router = useRouter()
  const [searchData, setSearchData] = useState({
    startDate: '',
    endDate: '',
    startPlace: '',
    endPlace: '',
  })

  const handleSearch = () => {
    const params = new URLSearchParams(searchData)
    router.push(`/catalog?${params.toString()}`)
  }

  return (
    <section className="relative bg-gradient-to-br from-blue-50 to-purple-50 py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Premium Car Rental
            <span className="block text-primary">Made Simple</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Experience luxury and convenience with our premium fleet of vehicles. 
            Book your perfect ride in just a few clicks.
          </p>
        </div>

        <Card className="max-w-4xl mx-auto shadow-xl">
          <CardContent className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Start Date
                </label>
                <Input
                  type="date"
                  value={searchData.startDate}
                  onChange={(e) => setSearchData({ ...searchData, startDate: e.target.value })}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  End Date
                </label>
                <Input
                  type="date"
                  value={searchData.endDate}
                  onChange={(e) => setSearchData({ ...searchData, endDate: e.target.value })}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <MapPin className="h-4 w-4 mr-2" />
                  Pickup Location
                </label>
                <Input
                  placeholder="Enter pickup location"
                  value={searchData.startPlace}
                  onChange={(e) => setSearchData({ ...searchData, startPlace: e.target.value })}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <MapPin className="h-4 w-4 mr-2" />
                  Return Location
                </label>
                <Input
                  placeholder="Enter return location"
                  value={searchData.endPlace}
                  onChange={(e) => setSearchData({ ...searchData, endPlace: e.target.value })}
                  className="w-full"
                />
              </div>
            </div>

            <div className="mt-8 flex justify-center">
              <Button
                onClick={handleSearch}
                size="lg"
                className="bg-primary hover:bg-primary/90 text-white px-12 py-3 text-lg font-semibold flex items-center"
              >
                <Search className="h-5 w-5 mr-2" />
                Search Cars
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Car className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Premium Fleet</h3>
            <p className="text-gray-600">Choose from our collection of luxury and economy vehicles</p>
          </div>

          <div className="p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Fully Insured</h3>
            <p className="text-gray-600">All vehicles come with comprehensive insurance coverage</p>
          </div>

          <div className="p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
            <p className="text-gray-600">Round-the-clock customer support for your peace of mind</p>
          </div>
        </div>
      </div>
    </section>
  )
}