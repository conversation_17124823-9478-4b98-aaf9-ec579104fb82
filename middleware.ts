import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const isAuth = !!token
    const isAuthPage = req.nextUrl.pathname.startsWith('/auth')
    const isOnboardingPage = req.nextUrl.pathname.startsWith('/onboarding')
    const isApiRoute = req.nextUrl.pathname.startsWith('/api')
    const isPublicPage = ['/', '/catalog'].includes(req.nextUrl.pathname)

    if (isApiRoute) {
      return NextResponse.next()
    }

    if (isAuthPage) {
      if (isAuth) {
        return NextResponse.redirect(new URL('/', req.url))
      }
      return NextResponse.next()
    }

    if (!isAuth && !isPublicPage) {
      let from = req.nextUrl.pathname
      if (req.nextUrl.search) {
        from += req.nextUrl.search
      }
      return NextResponse.redirect(
        new URL(`/auth/signin?from=${encodeURIComponent(from)}`, req.url)
      )
    }

    if (isAuth && !token?.isOnboarded && !isOnboardingPage && !isPublicPage) {
      return NextResponse.redirect(new URL('/onboarding', req.url))
    }

    if (isAuth && token?.isOnboarded && isOnboardingPage) {
      return NextResponse.redirect(new URL('/', req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: () => true,
    },
  }
)

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
}